package com.campustrade.dao;

import com.campustrade.model.User;
import com.campustrade.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户数据访问对象
 * 负责用户相关的数据库操作
 *
 * <AUTHOR>
 * @version 1.0
 */
public class UserDAO {

    /**
     * 插入新用户（注册）
     *
     * @param user 用户对象
     * @return 插入成功返回生成的用户ID，失败返回null
     */
    public Integer insert(User user) {
        String sql = "INSERT INTO users (username, password, email, phone, credit_score) VALUES (?, ?, ?, ?, ?)";

        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);

            // 设置参数
            pstmt.setString(1, user.getUsername());
            pstmt.setString(2, user.getPassword());
            pstmt.setString(3, user.getEmail());
            pstmt.setString(4, user.getPhone());
            pstmt.setInt(5, user.getCreditScore() != null ? user.getCreditScore() : 100);

            // 执行插入
            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                // 获取生成的主键
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    Integer userId = rs.getInt(1);
                    user.setUserId(userId);
                    return userId;
                }
            }
        } catch (SQLException e) {
            System.err.println("Failed to insert user: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }

        return null;
    }

    /**
     * 根据用户名和密码查找用户（登录验证）
     *
     * @param username 用户名
     * @param password 密码（已加密）
     * @return 找到返回用户对象，否则返回null
     */
    public User findByUsernameAndPassword(String username, String password) {
        String sql = "SELECT * FROM users WHERE username = ? AND password = ?";

        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, password);

            rs = pstmt.executeQuery();

            if (rs.next()) {
                return mapResultSetToUser(rs);
            }
        } catch (SQLException e) {
            System.err.println("查询用户失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }

        return null;
    }

    /**
     * 根据用户ID查找用户
     *
     * @param userId 用户ID
     * @return 找到返回用户对象，否则返回null
     */
    public User findById(Integer userId) {
        String sql = "SELECT * FROM users WHERE user_id = ?";

        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, userId);

            rs = pstmt.executeQuery();

            if (rs.next()) {
                return mapResultSetToUser(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据ID查询用户失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }

        return null;
    }

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 找到返回用户对象，否则返回null
     */
    public User findByUsername(String username) {
        String sql = "SELECT * FROM users WHERE username = ?";

        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);

            rs = pstmt.executeQuery();

            if (rs.next()) {
                return mapResultSetToUser(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据用户名查询用户失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }

        return null;
    }

    /**
     * 更新用户信息
     *
     * @param user 用户对象
     * @return 更新成功返回true，失败返回false
     */
    public boolean update(User user) {
        String sql = "UPDATE users SET email = ?, phone = ? WHERE user_id = ?";

        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);

            pstmt.setString(1, user.getEmail());
            pstmt.setString(2, user.getPhone());
            pstmt.setInt(3, user.getUserId());

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("更新用户信息失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }

        return false;
    }

    /**
     * 更新用户信誉分
     *
     * @param userId 用户ID
     * @param creditScore 新的信誉分
     * @return 更新成功返回true，失败返回false
     */
    public boolean updateCreditScore(Integer userId, Integer creditScore) {
        String sql = "UPDATE users SET credit_score = ? WHERE user_id = ?";

        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);

            pstmt.setInt(1, creditScore);
            pstmt.setInt(2, userId);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("更新用户信誉分失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }

        return false;
    }

    /**
     * 查询所有用户
     *
     * @return 用户列表
     */
    public List<User> findAll() {
        String sql = "SELECT * FROM users ORDER BY create_time DESC";
        List<User> users = new ArrayList<>();

        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();

            while (rs.next()) {
                users.add(mapResultSetToUser(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询所有用户失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }

        return users;
    }

    /**
     * 将ResultSet映射为User对象
     *
     * @param rs ResultSet对象
     * @return User对象
     * @throws SQLException SQL异常
     */
    private User mapResultSetToUser(ResultSet rs) throws SQLException {
        User user = new User();
        user.setUserId(rs.getInt("user_id"));
        user.setUsername(rs.getString("username"));
        user.setPassword(rs.getString("password"));
        user.setEmail(rs.getString("email"));
        user.setPhone(rs.getString("phone"));
        user.setCreditScore(rs.getInt("credit_score"));
        user.setCreateTime(rs.getTimestamp("create_time"));
        user.setUpdateTime(rs.getTimestamp("update_time"));
        return user;
    }

    /**
     * 关闭数据库资源
     *
     * @param conn 数据库连接
     * @param pstmt 预编译语句
     * @param rs 结果集
     */
    private void closeResources(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet失败: " + e.getMessage());
            }
        }
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement失败: " + e.getMessage());
            }
        }
        if (conn != null) {
            DBUtil.closeConnection(conn);
        }
    }
}
