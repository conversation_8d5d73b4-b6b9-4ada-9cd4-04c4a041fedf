package com.campustrade.filter;

import com.campustrade.model.User;
import com.campustrade.util.ResponseUtil;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 登录验证过滤器
 * 检查用户是否已登录，未登录则重定向到登录页面
 *
 * <AUTHOR>
 * @version 1.0
 */
public class LoginFilter implements Filter {

    // 不需要登录验证的路径
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
        "/user/login",
        "/user/register",
        "/user/checkUsername",
        "/login.html",
        "/register.html",
        "/index.html",
        "/css/",
        "/js/",
        "/images/"
    );

    /**
     * 过滤器初始化
     *
     * @param filterConfig 过滤器配置
     * @throws ServletException Servlet异常
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        System.out.println("LoginFilter initialized");
    }

    /**
     * 执行过滤
     *
     * @param request 请求对象
     * @param response 响应对象
     * @param chain 过滤器链
     * @throws IOException IO异常
     * @throws ServletException Servlet异常
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        // 转换为HTTP请求和响应对象
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 获取请求路径
        String requestURI = httpRequest.getRequestURI();
        String contextPath = httpRequest.getContextPath();
        String path = requestURI.substring(contextPath.length());

        // 检查是否是不需要登录验证的路径
        if (isExcludePath(path)) {
            // 直接放行
            chain.doFilter(request, response);
            return;
        }

        // 获取Session
        HttpSession session = httpRequest.getSession(false);
        User currentUser = null;

        if (session != null) {
            currentUser = (User) session.getAttribute("currentUser");
        }

        // 检查用户是否已登录
        if (currentUser == null) {
            // 未登录，根据请求类型进行不同处理
            String requestType = getRequestType(httpRequest);

            if ("AJAX".equals(requestType)) {
                // AJAX请求，返回JSON错误信息
                ResponseUtil.sendJsonError(httpResponse, 401, "请先登录");
            } else {
                // 普通请求，重定向到登录页面
                String loginUrl = contextPath + "/login.html";
                httpResponse.sendRedirect(loginUrl);
            }
            return;
        }

        // 已登录，继续执行
        chain.doFilter(request, response);
    }

    /**
     * 检查是否是不需要登录验证的路径
     *
     * @param path 请求路径
     * @return 是排除路径返回true，否则返回false
     */
    private boolean isExcludePath(String path) {
        if (path == null || path.isEmpty()) {
            return false;
        }

        // 检查完全匹配
        if (EXCLUDE_PATHS.contains(path)) {
            return true;
        }

        // 检查前缀匹配（用于静态资源）
        for (String excludePath : EXCLUDE_PATHS) {
            if (excludePath.endsWith("/") && path.startsWith(excludePath)) {
                return true;
            }
        }

        // 根路径放行
        if ("/".equals(path) || "".equals(path)) {
            return true;
        }

        return false;
    }

    /**
     * 判断请求类型
     *
     * @param request HTTP请求对象
     * @return 请求类型字符串
     */
    private String getRequestType(HttpServletRequest request) {
        // 检查是否是AJAX请求
        String xRequestedWith = request.getHeader("X-Requested-With");
        if ("XMLHttpRequest".equals(xRequestedWith)) {
            return "AJAX";
        }

        // 检查Accept头
        String accept = request.getHeader("Accept");
        if (accept != null && accept.contains("application/json")) {
            return "AJAX";
        }

        // 检查Content-Type头
        String contentType = request.getContentType();
        if (contentType != null && contentType.contains("application/json")) {
            return "AJAX";
        }

        return "NORMAL";
    }

    /**
     * 过滤器销毁
     */
    @Override
    public void destroy() {
        System.out.println("LoginFilter destroyed");
    }
}
