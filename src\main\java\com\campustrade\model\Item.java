package com.campustrade.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 商品实体类
 * 对应数据库中的items表
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Item {
    
    // 商品状态常量
    public static final int STATUS_AVAILABLE = 1;  // 上架
    public static final int STATUS_OFFLINE = 2;    // 下架
    public static final int STATUS_SOLD = 3;       // 已售
    
    // 商品ID，主键
    private Integer itemId;
    
    // 卖家ID，外键关联用户表
    private Integer sellerId;
    
    // 商品标题
    private String title;
    
    // 商品描述
    private String description;
    
    // 商品价格
    private BigDecimal price;
    
    // 商品状态：1-上架，2-下架，3-已售
    private Integer status;
    
    // 发布时间
    private Timestamp createTime;
    
    // 更新时间
    private Timestamp updateTime;
    
    // 卖家信息（关联查询时使用）
    private User seller;
    
    /**
     * 无参构造函数
     */
    public Item() {
    }
    
    /**
     * 带参构造函数（用于发布商品）
     * 
     * @param sellerId 卖家ID
     * @param title 商品标题
     * @param description 商品描述
     * @param price 商品价格
     */
    public Item(Integer sellerId, String title, String description, BigDecimal price) {
        this.sellerId = sellerId;
        this.title = title;
        this.description = description;
        this.price = price;
        this.status = STATUS_AVAILABLE; // 默认上架状态
    }
    
    // Getter和Setter方法
    
    public Integer getItemId() {
        return itemId;
    }
    
    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }
    
    public Integer getSellerId() {
        return sellerId;
    }
    
    public void setSellerId(Integer sellerId) {
        this.sellerId = sellerId;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public Timestamp getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
    
    public User getSeller() {
        return seller;
    }
    
    public void setSeller(User seller) {
        this.seller = seller;
    }
    
    /**
     * 获取状态描述
     * 
     * @return 状态描述字符串
     */
    public String getStatusText() {
        switch (status) {
            case STATUS_AVAILABLE:
                return "上架中";
            case STATUS_OFFLINE:
                return "已下架";
            case STATUS_SOLD:
                return "已售出";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 检查商品是否可以购买
     * 
     * @return true表示可以购买，false表示不可购买
     */
    public boolean isAvailable() {
        return STATUS_AVAILABLE == status;
    }
    
    /**
     * 检查商品是否已售出
     * 
     * @return true表示已售出，false表示未售出
     */
    public boolean isSold() {
        return STATUS_SOLD == status;
    }
    
    /**
     * 重写toString方法，方便调试
     */
    @Override
    public String toString() {
        return "Item{" +
                "itemId=" + itemId +
                ", sellerId=" + sellerId +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", price=" + price +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
    
    /**
     * 重写equals方法，用于对象比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Item item = (Item) obj;
        return itemId != null ? itemId.equals(item.itemId) : item.itemId == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return itemId != null ? itemId.hashCode() : 0;
    }
}
