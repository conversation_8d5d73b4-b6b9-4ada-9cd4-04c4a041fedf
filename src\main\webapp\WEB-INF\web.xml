<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">

    <!-- 应用基本信息 -->
    <display-name>校园二手交易平台</display-name>
    <description>基于Servlet和JDBC的校园二手交易平台</description>

    <!-- 欢迎页面配置 -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!-- 数据库连接池配置 -->
    <context-param>
        <param-name>db.driver</param-name>
        <param-value>com.mysql.cj.jdbc.Driver</param-value>
    </context-param>
    <context-param>
        <param-name>db.url</param-name>
        <param-value>*************************************************************************************************************************************</param-value>
    </context-param>
    <context-param>
        <param-name>db.username</param-name>
        <param-value>root</param-value>
    </context-param>
    <context-param>
        <param-name>db.password</param-name>
        <param-value>root</param-value>
    </context-param>

    <!-- 用户相关Servlet配置 -->
    <servlet>
        <servlet-name>UserServlet</servlet-name>
        <servlet-class>com.campustrade.servlet.UserServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>UserServlet</servlet-name>
        <url-pattern>/user/*</url-pattern>
    </servlet-mapping>

    <!-- 商品相关Servlet配置 -->
    <servlet>
        <servlet-name>ItemServlet</servlet-name>
        <servlet-class>com.campustrade.servlet.ItemServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ItemServlet</servlet-name>
        <url-pattern>/item/*</url-pattern>
    </servlet-mapping>

    <!-- 订单相关Servlet配置 -->
    <servlet>
        <servlet-name>OrderServlet</servlet-name>
        <servlet-class>com.campustrade.servlet.OrderServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OrderServlet</servlet-name>
        <url-pattern>/order/*</url-pattern>
    </servlet-mapping>

    <!-- 评价相关Servlet配置 -->
    <servlet>
        <servlet-name>ReviewServlet</servlet-name>
        <servlet-class>com.campustrade.servlet.ReviewServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ReviewServlet</servlet-name>
        <url-pattern>/review/*</url-pattern>
    </servlet-mapping>

    <!-- 字符编码过滤器 -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>com.campustrade.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 登录验证过滤器 -->
    <filter>
        <filter-name>LoginFilter</filter-name>
        <filter-class>com.campustrade.filter.LoginFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>LoginFilter</filter-name>
        <url-pattern>/item/*</url-pattern>
        <url-pattern>/order/*</url-pattern>
        <url-pattern>/review/*</url-pattern>
        <url-pattern>/user/update</url-pattern>
        <url-pattern>/user/profile</url-pattern>
    </filter-mapping>

    <!-- Session超时配置（30分钟） -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <!-- 错误页面配置 -->
    <error-page>
        <error-code>404</error-code>
        <location>/error/404.html</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/error/500.html</location>
    </error-page>

</web-app>
