<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.campustrade.model.User" %>
<%
    // 检查是否已登录
    User currentUser = (User) session.getAttribute("currentUser");
    if (currentUser != null) {
        response.sendRedirect("index.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 校园二手交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #666;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-primary:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .form-footer {
            text-align: center;
            margin-top: 20px;
        }

        .form-footer a {
            color: #3498db;
            text-decoration: none;
        }

        .form-footer a:hover {
            text-decoration: underline;
        }

        .error-message {
            background-color: #e74c3c;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background-color: #27ae60;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .back-link:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 20px;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <a href="index.jsp" class="back-link">← 返回首页</a>

    <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">用户登录</h1>
            <p class="login-subtitle">欢迎回到校园二手交易平台</p>
        </div>

        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>
        <div id="loading" class="loading">正在登录...</div>

        <form id="loginForm" onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <input type="text" id="username" name="username" class="form-input" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input type="password" id="password" name="password" class="form-input" required>
            </div>

            <button type="submit" class="btn btn-primary" id="loginBtn">登录</button>
        </form>

        <div class="form-footer">
            <p>还没有账号？ <a href="register.jsp">立即注册</a></p>
        </div>
    </div>

    <script>
        // 处理登录表单提交
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            // 基本验证
            if (!username) {
                showErrorMessage('请输入用户名');
                return;
            }

            if (!password) {
                showErrorMessage('请输入密码');
                return;
            }

            // 显示加载状态
            showLoading(true);
            hideMessages();

            // 发送登录请求
            fetch('<%=request.getContextPath()%>/user/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'username=' + encodeURIComponent(username) + '&password=' + encodeURIComponent(password)
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);

                if (data.success) {
                    showSuccessMessage('登录成功！正在跳转...');

                    // 延迟跳转，让用户看到成功消息
                    setTimeout(() => {
                        // 检查是否有重定向参数
                        const urlParams = new URLSearchParams(window.location.search);
                        const redirect = urlParams.get('redirect');

                        if (redirect) {
                            window.location.href = decodeURIComponent(redirect);
                        } else {
                            window.location.href = 'index.jsp';
                        }
                    }, 1000);
                } else {
                    showErrorMessage(data.message || '登录失败，请检查用户名和密码');
                }
            })
            .catch(error => {
                showLoading(false);
                console.error('登录请求失败:', error);
                showErrorMessage('登录失败，请检查网络连接');
            });
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 3000);
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loadingDiv = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');

            if (show) {
                loadingDiv.style.display = 'block';
                loginBtn.disabled = true;
                loginBtn.textContent = '登录中...';
            } else {
                loadingDiv.style.display = 'none';
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        }

        // 回车键提交表单
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                const form = document.getElementById('loginForm');
                if (form) {
                    form.dispatchEvent(new Event('submit'));
                }
            }
        });
    </script>
</body>
</html>
