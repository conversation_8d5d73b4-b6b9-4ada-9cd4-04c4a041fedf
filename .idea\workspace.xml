<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\Env\apache-maven-3.9.9" />
        <option name="localRepository" value="E:\Env\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\Env\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settings.project.maven.importing&quot;
  }
}</component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <created>1746638827820</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746638827820</updated>
    </task>
    <servers />
  </component>
</project>