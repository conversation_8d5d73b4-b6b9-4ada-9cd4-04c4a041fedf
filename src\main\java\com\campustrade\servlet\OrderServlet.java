package com.campustrade.servlet;

import com.campustrade.model.Order;
import com.campustrade.model.User;
import com.campustrade.service.OrderService;
import com.campustrade.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 订单控制器Servlet
 * 处理订单相关的HTTP请求
 *
 * <AUTHOR>
 * @version 1.0
 */
public class OrderServlet extends HttpServlet {

    private OrderService orderService;

    /**
     * Servlet初始化
     *
     * @throws ServletException Servlet异常
     */
    @Override
    public void init() throws ServletException {
        super.init();
        orderService = new OrderService();
        System.out.println("OrderServlet initialized");
    }

    /**
     * 处理GET请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        try {
            if (pathInfo == null || "/".equals(pathInfo)) {
                // 获取我的订单列表
                getMyOrders(request, response);
            } else if ("/buy".equals(pathInfo)) {
                // 获取我的购买订单
                getBuyOrders(request, response);
            } else if ("/sell".equals(pathInfo)) {
                // 获取我的销售订单
                getSellOrders(request, response);
            } else if (pathInfo.startsWith("/detail/")) {
                // 获取订单详情
                getOrderDetail(request, response);
            } else if ("/statistics".equals(pathInfo)) {
                // 获取订单统计
                getOrderStatistics(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 处理POST请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        try {
            if ("/create".equals(pathInfo)) {
                // 创建订单
                createOrder(request, response);
            } else if ("/confirm".equals(pathInfo)) {
                // 确认订单（支付完成）
                confirmOrder(request, response);
            } else if ("/cancel".equals(pathInfo)) {
                // 取消订单
                cancelOrder(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 创建订单
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void createOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String itemIdStr = request.getParameter("itemId");

        // 参数验证
        if (itemIdStr == null || itemIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品ID不能为空");
            return;
        }

        try {
            Integer itemId = Integer.parseInt(itemIdStr);

            // 创建订单
            Order order = orderService.createOrder(currentUser.getUserId(), itemId);

            if (order != null) {
                ResponseUtil.sendJsonSuccess(response, "订单创建成功", order);
            } else {
                ResponseUtil.sendJsonError(response, "订单创建失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "商品ID格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("创建订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 确认订单（支付完成）
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void confirmOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String orderIdStr = request.getParameter("orderId");

        // 参数验证
        if (orderIdStr == null || orderIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "订单ID不能为空");
            return;
        }

        try {
            Integer orderId = Integer.parseInt(orderIdStr);

            // 确认订单
            boolean success = orderService.confirmOrder(orderId, currentUser.getUserId());

            if (success) {
                ResponseUtil.sendJsonSuccess(response, "订单确认成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "订单确认失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "订单ID格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("确认订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "确认订单失败: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void cancelOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String orderIdStr = request.getParameter("orderId");

        // 参数验证
        if (orderIdStr == null || orderIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "订单ID不能为空");
            return;
        }

        try {
            Integer orderId = Integer.parseInt(orderIdStr);

            // 取消订单
            boolean success = orderService.cancelOrder(orderId, currentUser.getUserId());

            if (success) {
                ResponseUtil.sendJsonSuccess(response, "订单取消成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "订单取消失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "订单ID格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("取消订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的订单列表（买家和卖家订单合并）
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getMyOrders(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            List<Order> buyOrders = orderService.getBuyerOrders(currentUser.getUserId());
            List<Order> sellOrders = orderService.getSellerOrders(currentUser.getUserId());

            // 合并订单列表
            buyOrders.addAll(sellOrders);

            ResponseUtil.sendJsonSuccess(response, buyOrders);
        } catch (Exception e) {
            System.err.println("获取订单列表失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取购买订单列表
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getBuyOrders(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            List<Order> orders = orderService.getBuyerOrders(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, orders);
        } catch (Exception e) {
            System.err.println("获取购买订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取购买订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取销售订单列表
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getSellOrders(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            List<Order> orders = orderService.getSellerOrders(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, orders);
        } catch (Exception e) {
            System.err.println("获取销售订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取销售订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单详情
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getOrderDetail(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        String pathInfo = request.getPathInfo();
        String orderIdStr = pathInfo.substring("/detail/".length());

        try {
            Integer orderId = Integer.parseInt(orderIdStr);

            // 检查用户是否有权限查看该订单
            if (!orderService.hasOrderPermission(orderId, currentUser.getUserId())) {
                ResponseUtil.sendJsonError(response, 403, "无权限查看该订单");
                return;
            }

            Order order = orderService.getOrderById(orderId);

            if (order != null) {
                ResponseUtil.sendJsonSuccess(response, order);
            } else {
                ResponseUtil.sendJsonError(response, 404, "订单不存在");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, 400, "订单ID格式错误");
        } catch (Exception e) {
            System.err.println("获取订单详情失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单统计信息
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getOrderStatistics(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            OrderService.OrderStatistics statistics = orderService.getOrderStatistics(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, statistics);
        } catch (Exception e) {
            System.err.println("获取订单统计失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取订单统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户
     *
     * @param request HTTP请求对象
     * @return 当前用户，未登录返回null
     */
    private User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute("currentUser");
        }
        return null;
    }
}
