package com.campustrade.servlet;

import com.campustrade.dao.ReviewDAO;
import com.campustrade.dao.OrderDAO;
import com.campustrade.model.Review;
import com.campustrade.model.Order;
import com.campustrade.model.User;
import com.campustrade.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 评价控制器Servlet
 * 处理评价相关的HTTP请求
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ReviewServlet extends HttpServlet {

    private ReviewDAO reviewDAO;
    private OrderDAO orderDAO;

    /**
     * Servlet初始化
     *
     * @throws ServletException Servlet异常
     */
    @Override
    public void init() throws ServletException {
        super.init();
        reviewDAO = new ReviewDAO();
        orderDAO = new OrderDAO();
        System.out.println("ReviewServlet initialized");
    }

    /**
     * 处理GET请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        try {
            if (pathInfo == null || "/".equals(pathInfo)) {
                // 获取我的评价列表
                getMyReviews(request, response);
            } else if ("/received".equals(pathInfo)) {
                // 获取收到的评价
                getReceivedReviews(request, response);
            } else if ("/given".equals(pathInfo)) {
                // 获取给出的评价
                getGivenReviews(request, response);
            } else if (pathInfo.startsWith("/seller/")) {
                // 获取卖家的评价列表
                getSellerReviews(request, response);
            } else if (pathInfo.startsWith("/order/")) {
                // 根据订单ID获取评价
                getReviewByOrder(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 处理POST请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        try {
            if ("/submit".equals(pathInfo)) {
                // 提交评价
                submitReview(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 提交评价
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void submitReview(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String orderIdStr = request.getParameter("orderId");
        String scoreStr = request.getParameter("score");
        String comment = request.getParameter("comment");

        // 参数验证
        if (orderIdStr == null || orderIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "订单ID不能为空");
            return;
        }
        if (scoreStr == null || scoreStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "评分不能为空");
            return;
        }

        try {
            Integer orderId = Integer.parseInt(orderIdStr);
            Integer score = Integer.parseInt(scoreStr);

            // 验证评分范围
            if (score < 1 || score > 5) {
                ResponseUtil.sendJsonError(response, "评分必须在1-5之间");
                return;
            }

            // 检查订单是否存在
            Order order = orderDAO.findById(orderId);
            if (order == null) {
                ResponseUtil.sendJsonError(response, "订单不存在");
                return;
            }

            // 检查是否是买家本人
            if (!currentUser.getUserId().equals(order.getBuyerId())) {
                ResponseUtil.sendJsonError(response, "只有买家可以评价");
                return;
            }

            // 检查订单是否已完成
            if (!order.canReview()) {
                ResponseUtil.sendJsonError(response, "只有已完成的订单才能评价");
                return;
            }

            // 检查是否已经评价过
            if (reviewDAO.existsByOrderId(orderId)) {
                ResponseUtil.sendJsonError(response, "该订单已经评价过了");
                return;
            }

            // 创建评价对象
            Review review = new Review(orderId, order.getBuyerId(), order.getSellerId(), score, comment);

            // 保存评价
            Integer reviewId = reviewDAO.insert(review);

            if (reviewId != null) {
                review.setReviewId(reviewId);
                ResponseUtil.sendJsonSuccess(response, "评价提交成功", review);
            } else {
                ResponseUtil.sendJsonError(response, "评价提交失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "参数格式错误");
        } catch (Exception e) {
            System.err.println("提交评价失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "提交评价失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的评价列表（收到的和给出的）
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getMyReviews(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            List<Review> receivedReviews = reviewDAO.findBySellerId(currentUser.getUserId());
            List<Review> givenReviews = reviewDAO.findByBuyerId(currentUser.getUserId());

            // 合并评价列表
            receivedReviews.addAll(givenReviews);

            ResponseUtil.sendJsonSuccess(response, receivedReviews);
        } catch (Exception e) {
            System.err.println("获取评价列表失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取评价列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取收到的评价
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getReceivedReviews(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            List<Review> reviews = reviewDAO.findBySellerId(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, reviews);
        } catch (Exception e) {
            System.err.println("获取收到的评价失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取收到的评价失败: " + e.getMessage());
        }
    }

    /**
     * 获取给出的评价
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getGivenReviews(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            List<Review> reviews = reviewDAO.findByBuyerId(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, reviews);
        } catch (Exception e) {
            System.err.println("获取给出的评价失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取给出的评价失败: " + e.getMessage());
        }
    }

    /**
     * 获取卖家的评价列表
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getSellerReviews(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String pathInfo = request.getPathInfo();
        String sellerIdStr = pathInfo.substring("/seller/".length());

        try {
            Integer sellerId = Integer.parseInt(sellerIdStr);

            List<Review> reviews = reviewDAO.findBySellerId(sellerId);
            ResponseUtil.sendJsonSuccess(response, reviews);
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, 400, "卖家ID格式错误");
        } catch (Exception e) {
            System.err.println("获取卖家评价失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取卖家评价失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID获取评价
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getReviewByOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        String pathInfo = request.getPathInfo();
        String orderIdStr = pathInfo.substring("/order/".length());

        try {
            Integer orderId = Integer.parseInt(orderIdStr);

            // 检查订单是否存在
            Order order = orderDAO.findById(orderId);
            if (order == null) {
                ResponseUtil.sendJsonError(response, 404, "订单不存在");
                return;
            }

            // 检查用户是否有权限查看该订单的评价
            if (!currentUser.getUserId().equals(order.getBuyerId()) &&
                !currentUser.getUserId().equals(order.getSellerId())) {
                ResponseUtil.sendJsonError(response, 403, "无权限查看该评价");
                return;
            }

            Review review = reviewDAO.findByOrderId(orderId);

            if (review != null) {
                ResponseUtil.sendJsonSuccess(response, review);
            } else {
                ResponseUtil.sendJsonError(response, 404, "该订单暂无评价");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, 400, "订单ID格式错误");
        } catch (Exception e) {
            System.err.println("获取订单评价失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取订单评价失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户
     *
     * @param request HTTP请求对象
     * @return 当前用户，未登录返回null
     */
    private User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute("currentUser");
        }
        return null;
    }
}
