package com.campustrade.util;

import org.apache.commons.dbcp2.BasicDataSource;

import javax.servlet.ServletContext;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库连接工具类
 * 使用Apache DBCP2连接池管理数据库连接
 *
 * <AUTHOR>
 * @version 1.0
 */
public class DBUtil {

    // 数据库连接池实例
    private static BasicDataSource dataSource;

    /**
     * 初始化数据库连接池
     * 在应用启动时调用，通常在Servlet的init方法中
     *
     * @param context Servlet上下文，用于获取配置参数
     */
    public static void initDataSource(ServletContext context) {
        if (dataSource == null) {
            synchronized (DBUtil.class) {
                if (dataSource == null) {
                    // 创建数据源
                    dataSource = new BasicDataSource();

                    // 从web.xml中获取数据库配置参数
                    String driver = context.getInitParameter("db.driver");
                    String url = context.getInitParameter("db.url");
                    String username = context.getInitParameter("db.username");
                    String password = context.getInitParameter("db.password");

                    // 设置数据库连接参数
                    dataSource.setDriverClassName(driver);
                    dataSource.setUrl(url);
                    dataSource.setUsername(username);
                    dataSource.setPassword(password);

                    // 设置连接池参数
                    dataSource.setInitialSize(5);          // 初始连接数
                    dataSource.setMaxTotal(20);            // 最大连接数
                    dataSource.setMaxIdle(10);             // 最大空闲连接数
                    dataSource.setMinIdle(2);              // 最小空闲连接数
                    dataSource.setMaxWaitMillis(10000);    // 最大等待时间（毫秒）

                    // 设置连接验证
                    dataSource.setValidationQuery("SELECT 1");
                    dataSource.setTestOnBorrow(true);      // 获取连接时验证
                    dataSource.setTestWhileIdle(true);     // 空闲时验证

                    System.out.println("Database connection pool initialized successfully");
                }
            }
        }
    }

    /**
     * 获取数据库连接
     *
     * @return 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("Database connection pool not initialized");
        }
        return dataSource.getConnection();
    }

    /**
     * 关闭数据库连接
     * 实际上是将连接归还给连接池
     *
     * @param connection 数据库连接
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                System.err.println("Failed to close database connection: " + e.getMessage());
            }
        }
    }

    /**
     * 关闭数据库连接池
     * 在应用关闭时调用
     */
    public static void closeDataSource() {
        if (dataSource != null) {
            try {
                dataSource.close();
                System.out.println("Database connection pool closed");
            } catch (SQLException e) {
                System.err.println("Failed to close database connection pool: " + e.getMessage());
            }
        }
    }

    /**
     * 获取连接池状态信息
     * 用于监控和调试
     *
     * @return 连接池状态字符串
     */
    public static String getDataSourceStatus() {
        if (dataSource == null) {
            return "Database connection pool not initialized";
        }

        StringBuilder status = new StringBuilder();
        status.append("Connection pool status: ");
        status.append("Active=").append(dataSource.getNumActive());
        status.append(", Idle=").append(dataSource.getNumIdle());
        status.append(", Max=").append(dataSource.getMaxTotal());

        return status.toString();
    }
}
