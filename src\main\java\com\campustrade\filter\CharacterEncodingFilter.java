package com.campustrade.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 字符编码过滤器
 * 统一设置请求和响应的字符编码为UTF-8
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CharacterEncodingFilter implements Filter {

    // 默认字符编码
    private String encoding = "UTF-8";

    /**
     * 过滤器初始化
     *
     * @param filterConfig 过滤器配置
     * @throws ServletException Servlet异常
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 从配置中获取字符编码，如果没有配置则使用默认值
        String configEncoding = filterConfig.getInitParameter("encoding");
        if (configEncoding != null && !configEncoding.trim().isEmpty()) {
            this.encoding = configEncoding.trim();
        }

        System.out.println("CharacterEncodingFilter initialized, encoding: " + this.encoding);
    }

    /**
     * 执行过滤
     *
     * @param request 请求对象
     * @param response 响应对象
     * @param chain 过滤器链
     * @throws IOException IO异常
     * @throws ServletException Servlet异常
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        // 转换为HTTP请求和响应对象
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 强制设置请求字符编码为UTF-8
        request.setCharacterEncoding(encoding);

        // 设置响应字符编码和内容类型
        response.setCharacterEncoding(encoding);
        httpResponse.setHeader("Content-Type", "text/html;charset=" + encoding);

        // 继续执行过滤器链
        chain.doFilter(request, response);
    }

    /**
     * 过滤器销毁
     */
    @Override
    public void destroy() {
        System.out.println("CharacterEncodingFilter destroyed");
    }
}
