package com.campustrade.servlet;

import com.campustrade.model.User;
import com.campustrade.service.UserService;
import com.campustrade.util.DBUtil;
import com.campustrade.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 用户控制器Servlet
 * 处理用户相关的HTTP请求
 *
 * <AUTHOR>
 * @version 1.0
 */
public class UserServlet extends HttpServlet {

    private UserService userService;

    /**
     * Servlet初始化
     *
     * @throws ServletException Servlet异常
     */
    @Override
    public void init() throws ServletException {
        super.init();

        // 初始化数据库连接池
        DBUtil.initDataSource(getServletContext());

        // 初始化用户服务
        userService = new UserService();

        System.out.println("UserServlet initialized");
    }

    /**
     * 处理GET请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        try {
            if (pathInfo == null || "/".equals(pathInfo)) {
                // 获取当前用户信息
                getCurrentUser(request, response);
            } else if ("/profile".equals(pathInfo)) {
                // 获取用户详细信息
                getUserProfile(request, response);
            } else if ("/list".equals(pathInfo)) {
                // 获取用户列表（管理员功能）
                getUserList(request, response);
            } else if ("/checkUsername".equals(pathInfo)) {
                // 检查用户名是否可用
                checkUsername(request, response);
            } else if ("/logout".equals(pathInfo)) {
                // 用户登出
                logout(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 处理POST请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        try {
            if ("/register".equals(pathInfo)) {
                // 用户注册
                register(request, response);
            } else if ("/login".equals(pathInfo)) {
                // 用户登录
                login(request, response);
            } else if ("/update".equals(pathInfo)) {
                // 更新用户信息
                updateUser(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void register(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 获取请求参数
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        String email = request.getParameter("email");
        String phone = request.getParameter("phone");

        try {
            // 验证输入参数
            String validationError = userService.validateUserInput(username, password, email, phone);
            if (validationError != null) {
                ResponseUtil.sendJsonError(response, validationError);
                return;
            }

            // 执行注册
            User user = userService.register(username, password, email, phone);

            if (user != null) {
                // 注册成功，自动登录
                HttpSession session = request.getSession();
                session.setAttribute("currentUser", user);

                ResponseUtil.sendJsonSuccess(response, "注册成功", user);
            } else {
                ResponseUtil.sendJsonError(response, "注册失败，请稍后重试");
            }
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("用户注册失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "注册失败: " + e.getMessage());
        }
    }

    /**
     * 用户登录
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void login(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 获取请求参数
        String username = request.getParameter("username");
        String password = request.getParameter("password");

        try {
            // 执行登录
            User user = userService.login(username, password);

            if (user != null) {
                // 登录成功，创建Session
                HttpSession session = request.getSession();
                session.setAttribute("currentUser", user);

                ResponseUtil.sendJsonSuccess(response, "登录成功", user);
            } else {
                ResponseUtil.sendJsonError(response, "用户名或密码错误");
            }
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("用户登录失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void logout(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 销毁Session
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }

        ResponseUtil.sendJsonSuccess(response, "登出成功", null);
    }

    /**
     * 获取当前用户信息
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getCurrentUser(HttpServletRequest request, HttpServletResponse response) throws IOException {
        HttpSession session = request.getSession(false);
        if (session != null) {
            User currentUser = (User) session.getAttribute("currentUser");
            if (currentUser != null) {
                ResponseUtil.sendJsonSuccess(response, currentUser);
                return;
            }
        }

        ResponseUtil.sendJsonError(response, 401, "用户未登录");
    }

    /**
     * 获取用户详细信息
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getUserProfile(HttpServletRequest request, HttpServletResponse response) throws IOException {
        HttpSession session = request.getSession(false);
        if (session == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        User currentUser = (User) session.getAttribute("currentUser");
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            // 从数据库获取最新的用户信息
            User user = userService.getUserById(currentUser.getUserId());
            if (user != null) {
                ResponseUtil.sendJsonSuccess(response, user);
            } else {
                ResponseUtil.sendJsonError(response, "用户信息不存在");
            }
        } catch (Exception e) {
            System.err.println("获取用户信息失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void updateUser(HttpServletRequest request, HttpServletResponse response) throws IOException {
        HttpSession session = request.getSession(false);
        if (session == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        User currentUser = (User) session.getAttribute("currentUser");
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String email = request.getParameter("email");
        String phone = request.getParameter("phone");

        try {
            // 更新用户信息
            currentUser.setEmail(email);
            currentUser.setPhone(phone);

            boolean success = userService.updateUser(currentUser);

            if (success) {
                // 更新Session中的用户信息
                session.setAttribute("currentUser", currentUser);
                ResponseUtil.sendJsonSuccess(response, "更新成功", currentUser);
            } else {
                ResponseUtil.sendJsonError(response, "更新失败");
            }
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("更新用户信息失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void checkUsername(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String username = request.getParameter("username");

        if (username == null || username.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "用户名不能为空");
            return;
        }

        try {
            boolean available = userService.isUsernameAvailable(username.trim());
            ResponseUtil.sendJsonSuccess(response, available ? "用户名可用" : "用户名已存在", available);
        } catch (Exception e) {
            System.err.println("检查用户名失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "检查用户名失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户列表（管理员功能）
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getUserList(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            List<User> users = userService.getAllUsers();
            ResponseUtil.sendJsonSuccess(response, users);
        } catch (Exception e) {
            System.err.println("获取用户列表失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * Servlet销毁
     */
    @Override
    public void destroy() {
        // 关闭数据库连接池
        DBUtil.closeDataSource();
        System.out.println("UserServlet destroyed");
        super.destroy();
    }
}
