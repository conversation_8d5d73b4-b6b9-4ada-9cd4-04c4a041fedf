<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.campustrade.model.User" %>
<%
    // 检查是否已登录
    User currentUser = (User) session.getAttribute("currentUser");
    if (currentUser != null) {
        response.sendRedirect("index.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 校园二手交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .register-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .register-subtitle {
            color: #666;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-input.error {
            border-color: #e74c3c;
        }

        .form-input.success {
            border-color: #27ae60;
        }

        .input-group {
            position: relative;
        }

        .input-feedback {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 14px;
        }

        .feedback-success {
            color: #27ae60;
        }

        .feedback-error {
            color: #e74c3c;
        }

        .form-help {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #27ae60;
            color: white;
        }

        .btn-primary:hover {
            background-color: #229954;
        }

        .btn-primary:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .form-footer {
            text-align: center;
            margin-top: 20px;
        }

        .form-footer a {
            color: #3498db;
            text-decoration: none;
        }

        .form-footer a:hover {
            text-decoration: underline;
        }

        .error-message {
            background-color: #e74c3c;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background-color: #27ae60;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .back-link:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 480px) {
            .register-container {
                margin: 20px;
                padding: 30px 20px;
            }

            .register-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <a href="index.jsp" class="back-link">← 返回首页</a>

    <div class="register-container">
        <div class="register-header">
            <h1 class="register-title">用户注册</h1>
            <p class="register-subtitle">加入校园二手交易平台</p>
        </div>

        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>
        <div id="loading" class="loading">正在注册...</div>

        <form id="registerForm" onsubmit="handleRegister(event)">
            <div class="form-group">
                <label for="username" class="form-label">用户名 *</label>
                <div class="input-group">
                    <input type="text" id="username" name="username" class="form-input" required
                           onblur="checkUsername()" oninput="clearUsernameValidation()">
                    <span id="usernameStatus" class="input-feedback"></span>
                </div>
                <div class="form-help">3-20个字符，只能包含字母、数字和下划线</div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">密码 *</label>
                <input type="password" id="password" name="password" class="form-input" required
                       oninput="validatePassword()">
                <div class="form-help">至少6个字符</div>
            </div>

            <div class="form-group">
                <label for="confirmPassword" class="form-label">确认密码 *</label>
                <input type="password" id="confirmPassword" name="confirmPassword" class="form-input" required
                       oninput="validateConfirmPassword()">
            </div>

            <div class="form-group">
                <label for="email" class="form-label">邮箱 *</label>
                <input type="email" id="email" name="email" class="form-input" required>
                <div class="form-help">请输入有效的邮箱地址</div>
            </div>

            <div class="form-group">
                <label for="phone" class="form-label">手机号</label>
                <input type="tel" id="phone" name="phone" class="form-input">
                <div class="form-help">可选，用于联系</div>
            </div>

            <button type="submit" class="btn btn-primary" id="registerBtn" disabled>注册</button>
        </form>

        <div class="form-footer">
            <p>已有账号？ <a href="login.jsp">立即登录</a></p>
        </div>
    </div>

    <script>
        let usernameAvailable = false;

        // 检查用户名是否可用
        function checkUsername() {
            const username = document.getElementById('username').value.trim();
            const usernameInput = document.getElementById('username');
            const statusSpan = document.getElementById('usernameStatus');

            if (!username) {
                return;
            }

            if (username.length < 3 || username.length > 20) {
                usernameInput.className = 'form-input error';
                statusSpan.textContent = '✗';
                statusSpan.className = 'input-feedback feedback-error';
                usernameAvailable = false;
                updateRegisterButton();
                return;
            }

            // 检查用户名格式
            if (!/^[a-zA-Z0-9_]+$/.test(username)) {
                usernameInput.className = 'form-input error';
                statusSpan.textContent = '✗';
                statusSpan.className = 'input-feedback feedback-error';
                usernameAvailable = false;
                updateRegisterButton();
                return;
            }

            // 检查用户名是否已存在
            fetch('<%=request.getContextPath()%>/user/checkUsername?username=' + encodeURIComponent(username))
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        usernameInput.className = 'form-input success';
                        statusSpan.textContent = '✓';
                        statusSpan.className = 'input-feedback feedback-success';
                        usernameAvailable = true;
                    } else {
                        usernameInput.className = 'form-input error';
                        statusSpan.textContent = '✗';
                        statusSpan.className = 'input-feedback feedback-error';
                        usernameAvailable = false;
                    }
                    updateRegisterButton();
                })
                .catch(error => {
                    console.error('检查用户名失败:', error);
                    usernameInput.className = 'form-input error';
                    statusSpan.textContent = '?';
                    statusSpan.className = 'input-feedback feedback-error';
                    usernameAvailable = false;
                    updateRegisterButton();
                });
        }

        // 清除用户名验证状态
        function clearUsernameValidation() {
            const usernameInput = document.getElementById('username');
            const statusSpan = document.getElementById('usernameStatus');

            usernameInput.className = 'form-input';
            statusSpan.textContent = '';
            statusSpan.className = 'input-feedback';
            usernameAvailable = false;
            updateRegisterButton();
        }

        // 验证密码
        function validatePassword() {
            const password = document.getElementById('password').value;
            const passwordInput = document.getElementById('password');

            if (password.length >= 6) {
                passwordInput.className = 'form-input success';
            } else {
                passwordInput.className = 'form-input error';
            }

            validateConfirmPassword();
            updateRegisterButton();
        }

        // 验证确认密码
        function validateConfirmPassword() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const confirmPasswordInput = document.getElementById('confirmPassword');

            if (confirmPassword && password === confirmPassword) {
                confirmPasswordInput.className = 'form-input success';
            } else if (confirmPassword) {
                confirmPasswordInput.className = 'form-input error';
            } else {
                confirmPasswordInput.className = 'form-input';
            }

            updateRegisterButton();
        }

        // 更新注册按钮状态
        function updateRegisterButton() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const email = document.getElementById('email').value.trim();
            const registerBtn = document.getElementById('registerBtn');

            const isValid = usernameAvailable &&
                           password.length >= 6 &&
                           password === confirmPassword &&
                           email &&
                           /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

            registerBtn.disabled = !isValid;
        }

        // 处理注册表单提交
        function handleRegister(event) {
            event.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const email = document.getElementById('email').value.trim();
            const phone = document.getElementById('phone').value.trim();

            // 显示加载状态
            showLoading(true);
            hideMessages();

            fetch('<%=request.getContextPath()%>/user/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'username=' + encodeURIComponent(username) +
                      '&password=' + encodeURIComponent(password) +
                      '&email=' + encodeURIComponent(email) +
                      '&phone=' + encodeURIComponent(phone)
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);

                if (data.success) {
                    showSuccessMessage('注册成功！正在跳转到首页...');

                    // 延迟跳转
                    setTimeout(() => {
                        window.location.href = 'index.jsp';
                    }, 1500);
                } else {
                    showErrorMessage(data.message || '注册失败，请重试');
                }
            })
            .catch(error => {
                showLoading(false);
                console.error('注册请求失败:', error);
                showErrorMessage('注册失败，请检查网络连接');
            });
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loadingDiv = document.getElementById('loading');
            const registerBtn = document.getElementById('registerBtn');

            if (show) {
                loadingDiv.style.display = 'block';
                registerBtn.disabled = true;
                registerBtn.textContent = '注册中...';
            } else {
                loadingDiv.style.display = 'none';
                updateRegisterButton();
                registerBtn.textContent = '注册';
            }
        }

        // 监听输入变化
        document.getElementById('email').addEventListener('input', updateRegisterButton);
    </script>
</body>
</html>
