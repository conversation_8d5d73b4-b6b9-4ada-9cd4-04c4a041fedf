package com.campustrade.model;

import java.sql.Timestamp;

/**
 * 用户实体类
 * 对应数据库中的users表
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class User {
    
    // 用户ID，主键
    private Integer userId;
    
    // 用户名，唯一
    private String username;
    
    // 密码，MD5加密存储
    private String password;
    
    // 邮箱
    private String email;
    
    // 手机号
    private String phone;
    
    // 信誉分，初始值100
    private Integer creditScore;
    
    // 注册时间
    private Timestamp createTime;
    
    // 更新时间
    private Timestamp updateTime;
    
    /**
     * 无参构造函数
     */
    public User() {
    }
    
    /**
     * 带参构造函数（用于注册）
     * 
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱
     * @param phone 手机号
     */
    public User(String username, String password, String email, String phone) {
        this.username = username;
        this.password = password;
        this.email = email;
        this.phone = phone;
        this.creditScore = 100; // 默认信誉分
    }
    
    // Getter和Setter方法
    
    public Integer getUserId() {
        return userId;
    }
    
    public void setUserId(Integer userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public Integer getCreditScore() {
        return creditScore;
    }
    
    public void setCreditScore(Integer creditScore) {
        this.creditScore = creditScore;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public Timestamp getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
    
    /**
     * 重写toString方法，方便调试
     */
    @Override
    public String toString() {
        return "User{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", creditScore=" + creditScore +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
    
    /**
     * 重写equals方法，用于对象比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        User user = (User) obj;
        return userId != null ? userId.equals(user.userId) : user.userId == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return userId != null ? userId.hashCode() : 0;
    }
    
    /**
     * 获取信誉等级描述
     * 
     * @return 信誉等级字符串
     */
    public String getCreditLevel() {
        if (creditScore == null) {
            return "未知";
        }
        
        if (creditScore >= 150) {
            return "优秀";
        } else if (creditScore >= 120) {
            return "良好";
        } else if (creditScore >= 100) {
            return "一般";
        } else if (creditScore >= 80) {
            return "较差";
        } else {
            return "很差";
        }
    }
    
    /**
     * 检查用户信息是否完整
     * 
     * @return true表示信息完整，false表示信息不完整
     */
    public boolean isProfileComplete() {
        return username != null && !username.trim().isEmpty() &&
               email != null && !email.trim().isEmpty() &&
               phone != null && !phone.trim().isEmpty();
    }
}
