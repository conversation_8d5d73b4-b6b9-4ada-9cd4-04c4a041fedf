package com.campustrade.service;

import com.campustrade.dao.OrderDAO;
import com.campustrade.dao.ItemDAO;
import com.campustrade.model.Order;
import com.campustrade.model.Item;
import com.campustrade.util.DBUtil;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

/**
 * 订单业务逻辑服务类
 * 处理订单相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class OrderService {
    
    private OrderDAO orderDAO;
    private ItemDAO itemDAO;
    
    /**
     * 构造函数
     */
    public OrderService() {
        this.orderDAO = new OrderDAO();
        this.itemDAO = new ItemDAO();
    }
    
    /**
     * 创建订单
     * 使用事务确保数据一致性
     * 
     * @param buyerId 买家ID
     * @param itemId 商品ID
     * @return 创建成功返回订单对象，失败返回null
     */
    public Order createOrder(Integer buyerId, Integer itemId) {
        // 参数验证
        if (buyerId == null || buyerId <= 0) {
            throw new IllegalArgumentException("买家ID无效");
        }
        if (itemId == null || itemId <= 0) {
            throw new IllegalArgumentException("商品ID无效");
        }
        
        Connection conn = null;
        try {
            // 开启事务
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false);
            
            // 1. 检查商品是否存在且可购买
            Item item = itemDAO.findById(itemId);
            if (item == null) {
                throw new IllegalArgumentException("商品不存在");
            }
            if (!item.isAvailable()) {
                throw new IllegalArgumentException("商品不可购买");
            }
            
            // 2. 检查买家是否是卖家本人
            if (buyerId.equals(item.getSellerId())) {
                throw new IllegalArgumentException("不能购买自己发布的商品");
            }
            
            // 3. 检查是否已经购买过该商品
            if (orderDAO.existsByBuyerAndItem(buyerId, itemId)) {
                throw new IllegalArgumentException("您已经购买过该商品");
            }
            
            // 4. 创建订单
            Order order = new Order(buyerId, itemId, item.getSellerId(), item.getPrice());
            Integer orderId = orderDAO.insert(order);
            
            if (orderId == null) {
                throw new RuntimeException("创建订单失败");
            }
            
            // 5. 更新商品状态为已售
            boolean updateSuccess = itemDAO.updateStatus(itemId, Item.STATUS_SOLD);
            if (!updateSuccess) {
                throw new RuntimeException("更新商品状态失败");
            }
            
            // 提交事务
            conn.commit();
            
            // 返回完整的订单信息
            return orderDAO.findById(orderId);
            
        } catch (Exception e) {
            // 回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    System.err.println("事务回滚失败: " + rollbackEx.getMessage());
                }
            }
            throw new RuntimeException("创建订单失败: " + e.getMessage(), e);
        } finally {
            // 恢复自动提交并关闭连接
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    DBUtil.closeConnection(conn);
                } catch (SQLException e) {
                    System.err.println("关闭数据库连接失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 确认订单（支付完成）
     * 
     * @param orderId 订单ID
     * @param userId 操作用户ID（必须是买家）
     * @return 确认成功返回true，失败返回false
     */
    public boolean confirmOrder(Integer orderId, Integer userId) {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException("订单ID无效");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        // 查询订单
        Order order = orderDAO.findById(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在");
        }
        
        // 检查操作权限（只有买家可以确认订单）
        if (!userId.equals(order.getBuyerId())) {
            throw new IllegalArgumentException("无权限操作该订单");
        }
        
        // 检查订单状态
        if (!order.canCancel()) {
            throw new IllegalArgumentException("订单状态不允许确认");
        }
        
        // 更新订单状态为已完成
        return orderDAO.updateStatus(orderId, Order.STATUS_COMPLETED);
    }
    
    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @param userId 操作用户ID（买家或卖家）
     * @return 取消成功返回true，失败返回false
     */
    public boolean cancelOrder(Integer orderId, Integer userId) {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException("订单ID无效");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        Connection conn = null;
        try {
            // 开启事务
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false);
            
            // 查询订单
            Order order = orderDAO.findById(orderId);
            if (order == null) {
                throw new IllegalArgumentException("订单不存在");
            }
            
            // 检查操作权限（买家或卖家都可以取消）
            if (!userId.equals(order.getBuyerId()) && !userId.equals(order.getSellerId())) {
                throw new IllegalArgumentException("无权限操作该订单");
            }
            
            // 检查订单状态
            if (!order.canCancel()) {
                throw new IllegalArgumentException("订单状态不允许取消");
            }
            
            // 1. 更新订单状态为已取消
            boolean updateOrderSuccess = orderDAO.updateStatus(orderId, Order.STATUS_CANCELLED);
            if (!updateOrderSuccess) {
                throw new RuntimeException("更新订单状态失败");
            }
            
            // 2. 恢复商品状态为上架
            boolean updateItemSuccess = itemDAO.updateStatus(order.getItemId(), Item.STATUS_AVAILABLE);
            if (!updateItemSuccess) {
                throw new RuntimeException("恢复商品状态失败");
            }
            
            // 提交事务
            conn.commit();
            return true;
            
        } catch (Exception e) {
            // 回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    System.err.println("事务回滚失败: " + rollbackEx.getMessage());
                }
            }
            throw new RuntimeException("取消订单失败: " + e.getMessage(), e);
        } finally {
            // 恢复自动提交并关闭连接
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    DBUtil.closeConnection(conn);
                } catch (SQLException e) {
                    System.err.println("关闭数据库连接失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 根据订单ID获取订单信息
     * 
     * @param orderId 订单ID
     * @return 订单对象，不存在返回null
     */
    public Order getOrderById(Integer orderId) {
        if (orderId == null || orderId <= 0) {
            return null;
        }
        
        return orderDAO.findById(orderId);
    }
    
    /**
     * 获取买家的订单列表
     * 
     * @param buyerId 买家ID
     * @return 订单列表
     */
    public List<Order> getBuyerOrders(Integer buyerId) {
        if (buyerId == null || buyerId <= 0) {
            throw new IllegalArgumentException("买家ID无效");
        }
        
        return orderDAO.findByBuyerId(buyerId);
    }
    
    /**
     * 获取卖家的订单列表
     * 
     * @param sellerId 卖家ID
     * @return 订单列表
     */
    public List<Order> getSellerOrders(Integer sellerId) {
        if (sellerId == null || sellerId <= 0) {
            throw new IllegalArgumentException("卖家ID无效");
        }
        
        return orderDAO.findBySellerId(sellerId);
    }
    
    /**
     * 获取所有订单列表
     * 
     * @return 订单列表
     */
    public List<Order> getAllOrders() {
        return orderDAO.findAll();
    }
    
    /**
     * 检查用户是否有权限查看订单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 有权限返回true，无权限返回false
     */
    public boolean hasOrderPermission(Integer orderId, Integer userId) {
        if (orderId == null || userId == null) {
            return false;
        }
        
        Order order = orderDAO.findById(orderId);
        if (order == null) {
            return false;
        }
        
        // 买家或卖家都有权限查看
        return userId.equals(order.getBuyerId()) || userId.equals(order.getSellerId());
    }
    
    /**
     * 获取订单状态统计
     * 
     * @param userId 用户ID
     * @return 状态统计信息
     */
    public OrderStatistics getOrderStatistics(Integer userId) {
        if (userId == null || userId <= 0) {
            return new OrderStatistics();
        }
        
        List<Order> buyerOrders = orderDAO.findByBuyerId(userId);
        List<Order> sellerOrders = orderDAO.findBySellerId(userId);
        
        OrderStatistics stats = new OrderStatistics();
        
        // 统计买家订单
        for (Order order : buyerOrders) {
            stats.totalBuyOrders++;
            if (order.getStatus() == Order.STATUS_PENDING) {
                stats.pendingBuyOrders++;
            } else if (order.getStatus() == Order.STATUS_COMPLETED) {
                stats.completedBuyOrders++;
            }
        }
        
        // 统计卖家订单
        for (Order order : sellerOrders) {
            stats.totalSellOrders++;
            if (order.getStatus() == Order.STATUS_PENDING) {
                stats.pendingSellOrders++;
            } else if (order.getStatus() == Order.STATUS_COMPLETED) {
                stats.completedSellOrders++;
            }
        }
        
        return stats;
    }
    
    /**
     * 订单统计信息内部类
     */
    public static class OrderStatistics {
        public int totalBuyOrders = 0;      // 总购买订单数
        public int pendingBuyOrders = 0;    // 待支付购买订单数
        public int completedBuyOrders = 0;  // 已完成购买订单数
        public int totalSellOrders = 0;     // 总销售订单数
        public int pendingSellOrders = 0;   // 待支付销售订单数
        public int completedSellOrders = 0; // 已完成销售订单数
    }
}
