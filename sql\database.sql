-- 校园二手交易平台数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS campus_trade DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE campus_trade;

-- 删除已存在的表（按依赖关系倒序删除）
DROP TABLE IF EXISTS reviews;
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS items;
DROP TABLE IF EXISTS users;

-- 1. 用户表
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID，主键',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名，唯一',
    password VARCHAR(64) NOT NULL COMMENT '密码，MD5加密存储',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    credit_score INT DEFAULT 100 COMMENT '信誉分，初始值100',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 2. 商品表
CREATE TABLE items (
    item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID，主键',
    seller_id INT NOT NULL COMMENT '卖家ID，外键关联用户表',
    title VARCHAR(100) NOT NULL COMMENT '商品标题',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10, 2) NOT NULL COMMENT '商品价格',
    status TINYINT DEFAULT 1 COMMENT '商品状态：1-上架，2-下架，3-已售',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (seller_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 3. 订单表
CREATE TABLE orders (
    order_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID，主键',
    buyer_id INT NOT NULL COMMENT '买家ID，外键关联用户表',
    item_id INT NOT NULL COMMENT '商品ID，外键关联商品表',
    seller_id INT NOT NULL COMMENT '卖家ID，外键关联用户表',
    status TINYINT DEFAULT 1 COMMENT '订单状态：1-待支付，2-已完成，3-已取消',
    total_price DECIMAL(10, 2) NOT NULL COMMENT '订单总价',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (buyer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE,
    FOREIGN KEY (seller_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY unique_buyer_item (buyer_id, item_id) COMMENT '同一买家不能重复购买同一商品'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 4. 评价表
CREATE TABLE reviews (
    review_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '评价ID，主键',
    order_id INT UNIQUE NOT NULL COMMENT '订单ID，外键关联订单表，唯一约束',
    buyer_id INT NOT NULL COMMENT '买家ID，外键关联用户表',
    seller_id INT NOT NULL COMMENT '卖家ID，外键关联用户表',
    score TINYINT NOT NULL COMMENT '评分：1-5分',
    comment TEXT COMMENT '评价内容',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评价时间',
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (seller_id) REFERENCES users(user_id) ON DELETE CASCADE,
    CHECK (score BETWEEN 1 AND 5)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价表';

-- 创建索引提高查询性能
CREATE INDEX idx_items_seller_id ON items(seller_id);
CREATE INDEX idx_items_status ON items(status);
CREATE INDEX idx_orders_buyer_id ON orders(buyer_id);
CREATE INDEX idx_orders_seller_id ON orders(seller_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_reviews_seller_id ON reviews(seller_id);

-- 插入测试数据
-- 插入测试用户
INSERT INTO users (username, password, email, phone) VALUES
('admin', MD5('123456'), '<EMAIL>', '13800138000'),
('zhangsan', MD5('123456'), '<EMAIL>', '13800138001'),
('lisi', MD5('123456'), '<EMAIL>', '13800138002'),
('wangwu', MD5('123456'), '<EMAIL>', '13800138003');

-- 插入测试商品
INSERT INTO items (seller_id, title, description, price, status) VALUES
(2, '二手iPhone 12', '9成新，无磕碰，配件齐全', 3500.00, 1),
(2, '大学物理教材', '高等教育出版社，几乎全新', 45.00, 1),
(3, '自行车', '捷安特山地车，骑行2年，保养良好', 800.00, 1),
(3, '笔记本电脑', '联想ThinkPad，适合办公学习', 2800.00, 1);

-- 插入测试订单
INSERT INTO orders (buyer_id, item_id, seller_id, status, total_price) VALUES
(3, 2, 2, 2, 45.00),
(4, 3, 3, 2, 800.00);

-- 插入测试评价
INSERT INTO reviews (order_id, buyer_id, seller_id, score, comment) VALUES
(1, 3, 2, 5, '书的质量很好，卖家人也很nice！'),
(2, 4, 3, 4, '自行车状况不错，性价比很高');

-- 创建触发器：自动更新用户信誉分
DELIMITER $$
CREATE TRIGGER update_credit_score_after_review
AFTER INSERT ON reviews
FOR EACH ROW
BEGIN
    DECLARE avg_score DECIMAL(3,2);
    
    -- 计算卖家的平均评分
    SELECT AVG(score) INTO avg_score
    FROM reviews
    WHERE seller_id = NEW.seller_id;
    
    -- 更新卖家的信誉分（基础分100 + 平均分*20）
    UPDATE users 
    SET credit_score = 100 + ROUND(avg_score * 20)
    WHERE user_id = NEW.seller_id;
END$$
DELIMITER ;
