package com.campustrade.servlet;

import com.campustrade.dao.ItemDAO;
import com.campustrade.model.Item;
import com.campustrade.model.User;
import com.campustrade.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品控制器Servlet
 * 处理商品相关的HTTP请求
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ItemServlet extends HttpServlet {

    private ItemDAO itemDAO;

    /**
     * Servlet初始化
     *
     * @throws ServletException Servlet异常
     */
    @Override
    public void init() throws ServletException {
        super.init();
        itemDAO = new ItemDAO();
        System.out.println("ItemServlet initialized");
    }

    /**
     * 处理GET请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        try {
            if (pathInfo == null || "/".equals(pathInfo) || "/list".equals(pathInfo)) {
                // 获取商品列表
                getItemList(request, response);
            } else if (pathInfo.startsWith("/detail/")) {
                // 获取商品详情
                getItemDetail(request, response);
            } else if ("/my".equals(pathInfo)) {
                // 获取我的商品
                getMyItems(request, response);
            } else if ("/search".equals(pathInfo)) {
                // 搜索商品
                searchItems(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 处理POST请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();

        try {
            if ("/publish".equals(pathInfo)) {
                // 发布商品
                publishItem(request, response);
            } else if ("/update".equals(pathInfo)) {
                // 更新商品
                updateItem(request, response);
            } else if ("/updateStatus".equals(pathInfo)) {
                // 更新商品状态
                updateItemStatus(request, response);
            } else if ("/delete".equals(pathInfo)) {
                // 删除商品
                deleteItem(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 获取商品列表
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getItemList(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            List<Item> items = itemDAO.findAvailableItems();
            ResponseUtil.sendJsonSuccess(response, items);
        } catch (Exception e) {
            System.err.println("获取商品列表失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取商品列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品详情
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getItemDetail(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String pathInfo = request.getPathInfo();
        String itemIdStr = pathInfo.substring("/detail/".length());

        try {
            Integer itemId = Integer.parseInt(itemIdStr);
            Item item = itemDAO.findById(itemId);

            if (item != null) {
                ResponseUtil.sendJsonSuccess(response, item);
            } else {
                ResponseUtil.sendJsonError(response, 404, "商品不存在");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, 400, "商品ID格式错误");
        } catch (Exception e) {
            System.err.println("获取商品详情失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取商品详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的商品
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void getMyItems(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        try {
            List<Item> items = itemDAO.findBySellerId(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, items);
        } catch (Exception e) {
            System.err.println("获取我的商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取我的商品失败: " + e.getMessage());
        }
    }

    /**
     * 搜索商品
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void searchItems(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String keyword = request.getParameter("keyword");

        if (keyword == null || keyword.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "搜索关键词不能为空");
            return;
        }

        try {
            List<Item> items = itemDAO.searchByKeyword(keyword.trim());
            ResponseUtil.sendJsonSuccess(response, items);
        } catch (Exception e) {
            System.err.println("搜索商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "搜索商品失败: " + e.getMessage());
        }
    }

    /**
     * 发布商品
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void publishItem(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String title = request.getParameter("title");
        String description = request.getParameter("description");
        String priceStr = request.getParameter("price");

        // 参数验证
        if (title == null || title.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品标题不能为空");
            return;
        }
        if (priceStr == null || priceStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品价格不能为空");
            return;
        }

        try {
            BigDecimal price = new BigDecimal(priceStr);
            if (price.compareTo(BigDecimal.ZERO) <= 0) {
                ResponseUtil.sendJsonError(response, "商品价格必须大于0");
                return;
            }

            // 创建商品对象
            Item item = new Item(currentUser.getUserId(), title.trim(), description, price);

            // 保存到数据库
            Integer itemId = itemDAO.insert(item);

            if (itemId != null) {
                item.setItemId(itemId);
                ResponseUtil.sendJsonSuccess(response, "商品发布成功", item);
            } else {
                ResponseUtil.sendJsonError(response, "商品发布失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "价格格式错误");
        } catch (Exception e) {
            System.err.println("发布商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "发布商品失败: " + e.getMessage());
        }
    }

    /**
     * 更新商品信息
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void updateItem(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String itemIdStr = request.getParameter("itemId");
        String title = request.getParameter("title");
        String description = request.getParameter("description");
        String priceStr = request.getParameter("price");

        // 参数验证
        if (itemIdStr == null || itemIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品ID不能为空");
            return;
        }
        if (title == null || title.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品标题不能为空");
            return;
        }
        if (priceStr == null || priceStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品价格不能为空");
            return;
        }

        try {
            Integer itemId = Integer.parseInt(itemIdStr);
            BigDecimal price = new BigDecimal(priceStr);

            if (price.compareTo(BigDecimal.ZERO) <= 0) {
                ResponseUtil.sendJsonError(response, "商品价格必须大于0");
                return;
            }

            // 检查商品是否存在且属于当前用户
            Item existingItem = itemDAO.findById(itemId);
            if (existingItem == null) {
                ResponseUtil.sendJsonError(response, "商品不存在");
                return;
            }
            if (!currentUser.getUserId().equals(existingItem.getSellerId())) {
                ResponseUtil.sendJsonError(response, "无权限修改该商品");
                return;
            }

            // 更新商品信息
            existingItem.setTitle(title.trim());
            existingItem.setDescription(description);
            existingItem.setPrice(price);

            boolean success = itemDAO.update(existingItem);

            if (success) {
                ResponseUtil.sendJsonSuccess(response, "商品更新成功", existingItem);
            } else {
                ResponseUtil.sendJsonError(response, "商品更新失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "参数格式错误");
        } catch (Exception e) {
            System.err.println("更新商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "更新商品失败: " + e.getMessage());
        }
    }

    /**
     * 更新商品状态
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void updateItemStatus(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String itemIdStr = request.getParameter("itemId");
        String statusStr = request.getParameter("status");

        // 参数验证
        if (itemIdStr == null || itemIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品ID不能为空");
            return;
        }
        if (statusStr == null || statusStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品状态不能为空");
            return;
        }

        try {
            Integer itemId = Integer.parseInt(itemIdStr);
            Integer status = Integer.parseInt(statusStr);

            // 检查商品是否存在且属于当前用户
            Item existingItem = itemDAO.findById(itemId);
            if (existingItem == null) {
                ResponseUtil.sendJsonError(response, "商品不存在");
                return;
            }
            if (!currentUser.getUserId().equals(existingItem.getSellerId())) {
                ResponseUtil.sendJsonError(response, "无权限修改该商品");
                return;
            }

            // 检查状态值是否有效
            if (status != Item.STATUS_AVAILABLE && status != Item.STATUS_OFFLINE) {
                ResponseUtil.sendJsonError(response, "无效的商品状态");
                return;
            }

            // 更新商品状态
            boolean success = itemDAO.updateStatus(itemId, status);

            if (success) {
                String statusText = (status == Item.STATUS_AVAILABLE) ? "上架" : "下架";
                ResponseUtil.sendJsonSuccess(response, "商品" + statusText + "成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "更新商品状态失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "参数格式错误");
        } catch (Exception e) {
            System.err.println("更新商品状态失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "更新商品状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除商品
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void deleteItem(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        // 获取请求参数
        String itemIdStr = request.getParameter("itemId");

        // 参数验证
        if (itemIdStr == null || itemIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品ID不能为空");
            return;
        }

        try {
            Integer itemId = Integer.parseInt(itemIdStr);

            // 检查商品是否存在且属于当前用户
            Item existingItem = itemDAO.findById(itemId);
            if (existingItem == null) {
                ResponseUtil.sendJsonError(response, "商品不存在");
                return;
            }
            if (!currentUser.getUserId().equals(existingItem.getSellerId())) {
                ResponseUtil.sendJsonError(response, "无权限删除该商品");
                return;
            }

            // 检查商品是否已售出（已售出的商品不能删除）
            if (existingItem.isSold()) {
                ResponseUtil.sendJsonError(response, "已售出的商品不能删除");
                return;
            }

            // 删除商品
            boolean success = itemDAO.delete(itemId);

            if (success) {
                ResponseUtil.sendJsonSuccess(response, "商品删除成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "商品删除失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "商品ID格式错误");
        } catch (Exception e) {
            System.err.println("删除商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "删除商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户
     *
     * @param request HTTP请求对象
     * @return 当前用户，未登录返回null
     */
    private User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute("currentUser");
        }
        return null;
    }
}
