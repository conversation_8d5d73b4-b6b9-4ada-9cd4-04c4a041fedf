package com.campustrade.service;

import com.campustrade.dao.UserDAO;
import com.campustrade.model.User;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * 用户业务逻辑服务类
 * 处理用户相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class UserService {
    
    private UserDAO userDAO;
    
    /**
     * 构造函数
     */
    public UserService() {
        this.userDAO = new UserDAO();
    }
    
    /**
     * 用户注册
     * 
     * @param username 用户名
     * @param password 密码（明文）
     * @param email 邮箱
     * @param phone 手机号
     * @return 注册成功返回用户对象，失败返回null
     */
    public User register(String username, String password, String email, String phone) {
        // 参数验证
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("邮箱不能为空");
        }
        
        // 检查用户名是否已存在
        User existingUser = userDAO.findByUsername(username.trim());
        if (existingUser != null) {
            throw new IllegalArgumentException("用户名已存在");
        }
        
        // 密码加密
        String encryptedPassword = encryptPassword(password);
        
        // 创建用户对象
        User user = new User(username.trim(), encryptedPassword, email.trim(), phone != null ? phone.trim() : null);
        
        // 保存到数据库
        Integer userId = userDAO.insert(user);
        if (userId != null) {
            user.setUserId(userId);
            return user;
        }
        
        return null;
    }
    
    /**
     * 用户登录
     * 
     * @param username 用户名
     * @param password 密码（明文）
     * @return 登录成功返回用户对象，失败返回null
     */
    public User login(String username, String password) {
        // 参数验证
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        
        // 密码加密
        String encryptedPassword = encryptPassword(password);
        
        // 查询用户
        User user = userDAO.findByUsernameAndPassword(username.trim(), encryptedPassword);
        
        return user;
    }
    
    /**
     * 根据用户ID获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户对象，不存在返回null
     */
    public User getUserById(Integer userId) {
        if (userId == null || userId <= 0) {
            return null;
        }
        
        return userDAO.findById(userId);
    }
    
    /**
     * 根据用户名获取用户信息
     * 
     * @param username 用户名
     * @return 用户对象，不存在返回null
     */
    public User getUserByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return null;
        }
        
        return userDAO.findByUsername(username.trim());
    }
    
    /**
     * 更新用户信息
     * 
     * @param user 用户对象
     * @return 更新成功返回true，失败返回false
     */
    public boolean updateUser(User user) {
        if (user == null || user.getUserId() == null) {
            throw new IllegalArgumentException("用户信息不完整");
        }
        
        // 验证邮箱格式
        if (user.getEmail() != null && !user.getEmail().trim().isEmpty()) {
            if (!isValidEmail(user.getEmail().trim())) {
                throw new IllegalArgumentException("邮箱格式不正确");
            }
        }
        
        return userDAO.update(user);
    }
    
    /**
     * 更新用户信誉分
     * 
     * @param userId 用户ID
     * @param creditScore 新的信誉分
     * @return 更新成功返回true，失败返回false
     */
    public boolean updateCreditScore(Integer userId, Integer creditScore) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        if (creditScore == null || creditScore < 0) {
            throw new IllegalArgumentException("信誉分无效");
        }
        
        return userDAO.updateCreditScore(userId, creditScore);
    }
    
    /**
     * 获取所有用户列表
     * 
     * @return 用户列表
     */
    public List<User> getAllUsers() {
        return userDAO.findAll();
    }
    
    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @return 可用返回true，不可用返回false
     */
    public boolean isUsernameAvailable(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        User user = userDAO.findByUsername(username.trim());
        return user == null;
    }
    
    /**
     * 验证用户密码
     * 
     * @param userId 用户ID
     * @param password 密码（明文）
     * @return 密码正确返回true，错误返回false
     */
    public boolean validatePassword(Integer userId, String password) {
        if (userId == null || password == null) {
            return false;
        }
        
        User user = userDAO.findById(userId);
        if (user == null) {
            return false;
        }
        
        String encryptedPassword = encryptPassword(password);
        return encryptedPassword.equals(user.getPassword());
    }
    
    /**
     * 密码加密（使用MD5）
     * 
     * @param password 明文密码
     * @return 加密后的密码
     */
    private String encryptPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(password.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱地址
     * @return 格式正确返回true，错误返回false
     */
    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        // 简单的邮箱格式验证
        String emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.matches(emailPattern);
    }
    
    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     * @return 格式正确返回true，错误返回false
     */
    private boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return true; // 手机号可以为空
        }
        
        // 简单的手机号格式验证（11位数字）
        String phonePattern = "^1[3-9]\\d{9}$";
        return phone.matches(phonePattern);
    }
    
    /**
     * 验证用户输入的完整性
     * 
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱
     * @param phone 手机号
     * @return 验证通过返回null，否则返回错误信息
     */
    public String validateUserInput(String username, String password, String email, String phone) {
        if (username == null || username.trim().isEmpty()) {
            return "用户名不能为空";
        }
        if (username.trim().length() < 3 || username.trim().length() > 20) {
            return "用户名长度必须在3-20个字符之间";
        }
        if (password == null || password.trim().isEmpty()) {
            return "密码不能为空";
        }
        if (password.length() < 6) {
            return "密码长度不能少于6位";
        }
        if (email != null && !email.trim().isEmpty() && !isValidEmail(email.trim())) {
            return "邮箱格式不正确";
        }
        if (phone != null && !phone.trim().isEmpty() && !isValidPhone(phone.trim())) {
            return "手机号格式不正确";
        }
        
        return null; // 验证通过
    }
}
